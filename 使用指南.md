# POI兴趣点爬虫使用指南

## 项目简介

这是一个无需高德、百度API的兴趣点(POI)爬取工具，可以根据地区代码和关键词来获取各类兴趣点信息。

## 文件说明

### 核心文件
- `simple_poi_crawler.py` - 简化版爬虫（推荐使用）
- `enhanced_poi_crawler.py` - 增强版爬虫（功能更全面）
- `poi_crawler.py` - 基础版爬虫
- `config.py` - 配置文件
- `example.py` - 使用示例

### 依赖文件
- `requirements.txt` - Python依赖包列表
- `README.md` - 详细说明文档

## 快速开始

### 1. 安装依赖

```bash
pip install requests beautifulsoup4 lxml
```

或者使用requirements.txt：

```bash
pip install -r requirements.txt
```

### 2. 基础使用

#### 查看支持的地区代码
```bash
python simple_poi_crawler.py --list-regions
```

#### 查看支持的关键词
```bash
python simple_poi_crawler.py --list-keywords
```

#### 爬取POI数据
```bash
# 爬取北京市的餐厅信息
python simple_poi_crawler.py -r 110000 -k 餐厅

# 爬取上海市的酒店信息
python simple_poi_crawler.py -r 310000 -k 酒店

# 指定输出文件
python simple_poi_crawler.py -r 440100 -k 超市 -o guangzhou_supermarkets.csv
```

## 支持的地区代码

| 代码 | 地区 | 代码 | 地区 |
|------|------|------|------|
| 110000 | 北京 | 310000 | 上海 |
| 120000 | 天津 | 500000 | 重庆 |
| 440100 | 广州 | 440300 | 深圳 |
| 330100 | 杭州 | 320100 | 南京 |
| 510100 | 成都 | 420100 | 武汉 |
| 610100 | 西安 | 430100 | 长沙 |

*更多地区代码请运行 `--list-regions` 查看*

## 支持的关键词类型

### 餐饮类
餐厅、饭店、小吃、火锅、烧烤、快餐、咖啡厅

### 住宿类
酒店、宾馆、旅馆、民宿、青年旅社

### 购物类
超市、商场、便利店、专卖店、市场

### 医疗类
医院、诊所、药店、牙科、眼科

### 教育类
学校、幼儿园、培训机构、图书馆、大学

### 交通类
地铁站、公交站、火车站、机场、停车场

### 服务类
银行、ATM、邮局、理发店、美容院

### 娱乐类
电影院、KTV、游戏厅、健身房、公园

## 输出格式

生成的CSV文件包含以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| name | POI名称 | 北京烤鸭店 |
| display_name | 完整显示名称 | 北京烤鸭店1号店 |
| address | 地址 | 北京市朝阳区某某街道123号 |
| phone | 电话号码 | 010-12345678 |
| website | 网站 | http://example.com |
| category | 分类 | 餐厅 |
| type | 类型 | 中餐厅 |
| lat | 纬度 | 39.9042 |
| lon | 经度 | 116.4074 |
| source | 数据源 | Nominatim/OSM |

## 数据源说明

### 1. Nominatim/OpenStreetMap
- **优点**: 开源免费，数据质量高，包含坐标信息
- **缺点**: 中国地区数据相对较少
- **适用**: 获取基础地理信息和POI

### 2. 模拟数据
- **优点**: 确保有数据输出，格式统一
- **缺点**: 非真实数据，仅用于演示
- **适用**: 测试和演示场景

## 使用技巧

### 1. 提高数据质量
- 使用具体的关键词（如"川菜馆"而不是"餐厅"）
- 选择数据丰富的大城市
- 多次运行获取更多数据

### 2. 批量处理
```python
from simple_poi_crawler import SimplePOICrawler

crawler = SimplePOICrawler()

# 批量爬取多个城市的餐厅
cities = ['110000', '310000', '440100']  # 北京、上海、广州
for city in cities:
    pois = crawler.crawl_pois(city, '餐厅', f'restaurants_{city}.csv')
    print(f"{crawler.get_region_name(city)}: {len(pois)} 个餐厅")
```

### 3. 数据处理
```python
import pandas as pd

# 读取CSV文件
df = pd.read_csv('pois_110000_餐厅.csv')

# 筛选有坐标的POI
df_with_coords = df[(df['lat'] != '') & (df['lon'] != '')]

# 按数据源分组统计
source_stats = df.groupby('source').size()
print(source_stats)
```

## 常见问题

### Q1: 为什么获取的数据很少？
**A**: 这是正常现象，因为：
- OpenStreetMap在中国的POI数据相对较少
- 网络连接可能影响数据获取
- 建议尝试不同的关键词和地区

### Q2: 如何获取更多真实数据？
**A**: 可以考虑：
- 使用增强版爬虫 `enhanced_poi_crawler.py`
- 集成更多数据源
- 使用商业API（需要申请密钥）

### Q3: 数据准确性如何？
**A**: 
- Nominatim/OSM数据：较为准确，但数量有限
- 模拟数据：仅用于演示，不代表真实情况
- 建议结合多个数据源使用

### Q4: 如何处理网络超时？
**A**: 
- 检查网络连接
- 尝试使用VPN
- 增加超时时间设置

## 扩展开发

### 添加新的数据源
```python
def crawl_custom_source(self, region_name: str, keyword: str) -> List[Dict]:
    """自定义数据源爬取方法"""
    pois = []
    # 实现你的爬取逻辑
    return pois
```

### 自定义输出格式
```python
def save_to_json(self, pois: List[Dict], filename: str):
    """保存为JSON格式"""
    import json
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(pois, f, ensure_ascii=False, indent=2)
```

## 注意事项

1. **遵守法律法规**: 仅用于学习研究，不得用于商业用途
2. **尊重网站规则**: 遵守robots.txt和使用条款
3. **合理使用频率**: 避免过于频繁的请求
4. **数据验证**: 使用前请验证数据的准确性
5. **隐私保护**: 不要爬取和存储个人隐私信息

## 更新日志

- **v1.0** (2024-08-12): 初始版本，支持基础POI爬取
- **v1.1** (2024-08-12): 添加简化版爬虫，提高稳定性
- **v1.2** (2024-08-12): 完善文档和使用示例

## 技术支持

如果遇到问题，请：
1. 查看日志文件了解详细错误信息
2. 检查网络连接和防火墙设置
3. 尝试使用不同的参数组合
4. 参考示例代码进行调试

---

**免责声明**: 本工具仅供学习研究使用，使用者需自行承担使用风险，遵守相关法律法规。
