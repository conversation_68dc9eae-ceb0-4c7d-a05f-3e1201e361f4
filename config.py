#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
POI爬虫配置文件
"""

# 地区代码完整映射
REGION_CODES = {
    # 直辖市
    '110000': '北京市',
    '110100': '北京市市辖区',
    '120000': '天津市',
    '120100': '天津市市辖区',
    '310000': '上海市',
    '310100': '上海市市辖区',
    '500000': '重庆市',
    '500100': '重庆市市辖区',
    
    # 省份
    '130000': '河北省',
    '130100': '石家庄市',
    '130200': '唐山市',
    '130300': '秦皇岛市',
    '130400': '邯郸市',
    '130500': '邢台市',
    '130600': '保定市',
    '130700': '张家口市',
    '130800': '承德市',
    '130900': '沧州市',
    '131000': '廊坊市',
    '131100': '衡水市',
    
    '140000': '山西省',
    '140100': '太原市',
    '140200': '大同市',
    '140300': '阳泉市',
    '140400': '长治市',
    '140500': '晋城市',
    '140600': '朔州市',
    '140700': '晋中市',
    '140800': '运城市',
    '140900': '忻州市',
    '141000': '临汾市',
    '141100': '吕梁市',
    
    '210000': '辽宁省',
    '210100': '沈阳市',
    '210200': '大连市',
    '210300': '鞍山市',
    '210400': '抚顺市',
    '210500': '本溪市',
    '210600': '丹东市',
    '210700': '锦州市',
    '210800': '营口市',
    '210900': '阜新市',
    '211000': '辽阳市',
    '211100': '盘锦市',
    '211200': '铁岭市',
    '211300': '朝阳市',
    '211400': '葫芦岛市',
    
    '220000': '吉林省',
    '220100': '长春市',
    '220200': '吉林市',
    '220300': '四平市',
    '220400': '辽源市',
    '220500': '通化市',
    '220600': '白山市',
    '220700': '松原市',
    '220800': '白城市',
    
    '230000': '黑龙江省',
    '230100': '哈尔滨市',
    '230200': '齐齐哈尔市',
    '230300': '鸡西市',
    '230400': '鹤岗市',
    '230500': '双鸭山市',
    '230600': '大庆市',
    '230700': '伊春市',
    '230800': '佳木斯市',
    '230900': '七台河市',
    '231000': '牡丹江市',
    '231100': '黑河市',
    '231200': '绥化市',
    
    '320000': '江苏省',
    '320100': '南京市',
    '320200': '无锡市',
    '320300': '徐州市',
    '320400': '常州市',
    '320500': '苏州市',
    '320600': '南通市',
    '320700': '连云港市',
    '320800': '淮安市',
    '320900': '盐城市',
    '321000': '扬州市',
    '321100': '镇江市',
    '321200': '泰州市',
    '321300': '宿迁市',
    
    '330000': '浙江省',
    '330100': '杭州市',
    '330200': '宁波市',
    '330300': '温州市',
    '330400': '嘉兴市',
    '330500': '湖州市',
    '330600': '绍兴市',
    '330700': '金华市',
    '330800': '衢州市',
    '330900': '舟山市',
    '331000': '台州市',
    '331100': '丽水市',
    
    '340000': '安徽省',
    '340100': '合肥市',
    '340200': '芜湖市',
    '340300': '蚌埠市',
    '340400': '淮南市',
    '340500': '马鞍山市',
    '340600': '淮北市',
    '340700': '铜陵市',
    '340800': '安庆市',
    '341000': '黄山市',
    '341100': '滁州市',
    '341200': '阜阳市',
    '341300': '宿州市',
    '341500': '六安市',
    '341600': '亳州市',
    '341700': '池州市',
    '341800': '宣城市',
    
    '350000': '福建省',
    '350100': '福州市',
    '350200': '厦门市',
    '350300': '莆田市',
    '350400': '三明市',
    '350500': '泉州市',
    '350600': '漳州市',
    '350700': '南平市',
    '350800': '龙岩市',
    '350900': '宁德市',
    
    '360000': '江西省',
    '360100': '南昌市',
    '360200': '景德镇市',
    '360300': '萍乡市',
    '360400': '九江市',
    '360500': '新余市',
    '360600': '鹰潭市',
    '360700': '赣州市',
    '360800': '吉安市',
    '360900': '宜春市',
    '361000': '抚州市',
    '361100': '上饶市',
    
    '370000': '山东省',
    '370100': '济南市',
    '370200': '青岛市',
    '370300': '淄博市',
    '370400': '枣庄市',
    '370500': '东营市',
    '370600': '烟台市',
    '370700': '潍坊市',
    '370800': '济宁市',
    '370900': '泰安市',
    '371000': '威海市',
    '371100': '日照市',
    '371200': '莱芜市',
    '371300': '临沂市',
    '371400': '德州市',
    '371500': '聊城市',
    '371600': '滨州市',
    '371700': '菏泽市',
    
    '410000': '河南省',
    '410100': '郑州市',
    '410200': '开封市',
    '410300': '洛阳市',
    '410400': '平顶山市',
    '410500': '安阳市',
    '410600': '鹤壁市',
    '410700': '新乡市',
    '410800': '焦作市',
    '410900': '濮阳市',
    '411000': '许昌市',
    '411100': '漯河市',
    '411200': '三门峡市',
    '411300': '南阳市',
    '411400': '商丘市',
    '411500': '信阳市',
    '411600': '周口市',
    '411700': '驻马店市',
    
    '420000': '湖北省',
    '420100': '武汉市',
    '420200': '黄石市',
    '420300': '十堰市',
    '420500': '宜昌市',
    '420600': '襄阳市',
    '420700': '鄂州市',
    '420800': '荆门市',
    '420900': '孝感市',
    '421000': '荆州市',
    '421100': '黄冈市',
    '421200': '咸宁市',
    '421300': '随州市',
    
    '430000': '湖南省',
    '430100': '长沙市',
    '430200': '株洲市',
    '430300': '湘潭市',
    '430400': '衡阳市',
    '430500': '邵阳市',
    '430600': '岳阳市',
    '430700': '常德市',
    '430800': '张家界市',
    '430900': '益阳市',
    '431000': '郴州市',
    '431100': '永州市',
    '431200': '怀化市',
    '431300': '娄底市',
    
    '440000': '广东省',
    '440100': '广州市',
    '440200': '韶关市',
    '440300': '深圳市',
    '440400': '珠海市',
    '440500': '汕头市',
    '440600': '佛山市',
    '440700': '江门市',
    '440800': '湛江市',
    '440900': '茂名市',
    '441200': '肇庆市',
    '441300': '惠州市',
    '441400': '梅州市',
    '441500': '汕尾市',
    '441600': '河源市',
    '441700': '阳江市',
    '441800': '清远市',
    '441900': '东莞市',
    '442000': '中山市',
    '445100': '潮州市',
    '445200': '揭阳市',
    '445300': '云浮市',
    
    '450000': '广西壮族自治区',
    '450100': '南宁市',
    '450200': '柳州市',
    '450300': '桂林市',
    '450400': '梧州市',
    '450500': '北海市',
    '450600': '防城港市',
    '450700': '钦州市',
    '450800': '贵港市',
    '450900': '玉林市',
    '451000': '百色市',
    '451100': '贺州市',
    '451200': '河池市',
    '451300': '来宾市',
    '451400': '崇左市',
    
    '460000': '海南省',
    '460100': '海口市',
    '460200': '三亚市',
    '460300': '三沙市',
    '460400': '儋州市',
    
    '510000': '四川省',
    '510100': '成都市',
    '510300': '自贡市',
    '510400': '攀枝花市',
    '510500': '泸州市',
    '510600': '德阳市',
    '510700': '绵阳市',
    '510800': '广元市',
    '510900': '遂宁市',
    '511000': '内江市',
    '511100': '乐山市',
    '511300': '南充市',
    '511400': '眉山市',
    '511500': '宜宾市',
    '511600': '广安市',
    '511700': '达州市',
    '511800': '雅安市',
    '511900': '巴中市',
    '512000': '资阳市',
}

# 常用关键词分类
KEYWORD_CATEGORIES = {
    '餐饮': ['餐厅', '饭店', '小吃', '火锅', '烧烤', '快餐', '咖啡', '茶楼', '酒吧'],
    '购物': ['商场', '超市', '便利店', '专卖店', '市场', '商店', '购物中心'],
    '住宿': ['酒店', '宾馆', '旅馆', '民宿', '青年旅社', '度假村'],
    '交通': ['地铁站', '公交站', '火车站', '机场', '停车场', '加油站'],
    '医疗': ['医院', '诊所', '药店', '牙科', '眼科', '中医'],
    '教育': ['学校', '幼儿园', '培训机构', '图书馆', '大学', '中学', '小学'],
    '娱乐': ['电影院', 'KTV', '游戏厅', '网吧', '健身房', '游乐场'],
    '服务': ['银行', 'ATM', '邮局', '理发店', '美容院', '洗衣店', '维修店'],
    '景点': ['公园', '景区', '博物馆', '纪念馆', '寺庙', '教堂', '广场']
}

# 爬虫配置
CRAWLER_CONFIG = {
    'request_timeout': 15,  # 请求超时时间(秒)
    'retry_times': 3,       # 重试次数
    'delay_range': (1, 3),  # 请求间隔随机范围(秒)
    'max_pois_per_source': 100,  # 每个数据源最大POI数量
    'enable_osm': True,     # 是否启用OpenStreetMap
    'enable_dianping': True, # 是否启用大众点评
    'enable_58': True,      # 是否启用58同城
}

# 输出配置
OUTPUT_CONFIG = {
    'csv_encoding': 'utf-8',
    'include_coordinates': True,  # 是否包含坐标信息
    'include_source': True,       # 是否包含数据源信息
}
