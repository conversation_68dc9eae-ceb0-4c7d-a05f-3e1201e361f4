#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版POI爬虫 - 专注于可靠的数据源
"""

import requests
import json
import time
import csv
import re
import argparse
from urllib.parse import quote
from bs4 import BeautifulSoup
import logging
from typing import List, Dict
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimplePOICrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        
        # 地区代码映射（主要城市）
        self.region_codes = {
            '110000': '北京',
            '120000': '天津',
            '310000': '上海',
            '500000': '重庆',
            '130100': '石家庄',
            '140100': '太原',
            '210100': '沈阳',
            '220100': '长春',
            '230100': '哈尔滨',
            '320100': '南京',
            '330100': '杭州',
            '340100': '合肥',
            '350100': '福州',
            '360100': '南昌',
            '370100': '济南',
            '410100': '郑州',
            '420100': '武汉',
            '430100': '长沙',
            '440100': '广州',
            '440300': '深圳',
            '450100': '南宁',
            '460100': '海口',
            '510100': '成都',
            '520100': '贵阳',
            '530100': '昆明',
            '610100': '西安',
            '620100': '兰州',
            '630100': '西宁',
            '640100': '银川',
            '650100': '乌鲁木齐'
        }

    def get_region_name(self, region_code: str) -> str:
        """根据地区代码获取地区名称"""
        return self.region_codes.get(region_code, region_code)

    def crawl_nominatim_pois(self, region_name: str, keyword: str, limit: int = 50) -> List[Dict]:
        """
        使用Nominatim API搜索POI（OpenStreetMap的地理编码服务）
        """
        pois = []
        try:
            # Nominatim搜索API
            nominatim_url = "https://nominatim.openstreetmap.org/search"
            
            params = {
                'q': f"{keyword} {region_name}",
                'format': 'json',
                'limit': limit,
                'addressdetails': 1,
                'extratags': 1,
                'namedetails': 1,
                'countrycodes': 'cn'  # 限制在中国
            }
            
            response = self.session.get(nominatim_url, params=params, timeout=15)
            if response.status_code == 200:
                data = response.json()
                
                for item in data:
                    display_name = item.get('display_name', '')
                    if not display_name:
                        continue
                        
                    # 提取POI信息
                    poi = {
                        'name': item.get('name', ''),
                        'display_name': display_name,
                        'address': self._extract_address_from_display_name(display_name),
                        'phone': '',
                        'website': '',
                        'category': item.get('category', ''),
                        'type': item.get('type', ''),
                        'lat': item.get('lat', ''),
                        'lon': item.get('lon', ''),
                        'source': 'Nominatim/OSM'
                    }
                    
                    if poi['name']:  # 只保留有名称的POI
                        pois.append(poi)
                        
            logger.info(f"从Nominatim获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"Nominatim爬取失败: {e}")
            
        return pois

    def _extract_address_from_display_name(self, display_name: str) -> str:
        """从display_name中提取地址"""
        # 移除最后的国家信息
        parts = display_name.split(', ')
        if len(parts) > 1 and parts[-1] in ['中国', 'China']:
            parts = parts[:-1]
        
        # 返回前几个部分作为地址
        return ', '.join(parts[:3]) if len(parts) > 1 else display_name

    def crawl_mock_local_pois(self, region_name: str, keyword: str, limit: int = 20) -> List[Dict]:
        """
        模拟本地POI数据（用于演示）
        在实际应用中，这里可以连接本地数据库或其他数据源
        """
        pois = []
        
        # 模拟数据模板
        mock_templates = {
            '餐厅': [
                '老北京炸酱面', '川菜馆', '粤菜酒楼', '火锅店', '烧烤店',
                '西餐厅', '日料店', '韩式料理', '东北菜馆', '湘菜馆'
            ],
            '酒店': [
                '商务酒店', '快捷酒店', '度假酒店', '精品酒店', '青年旅社',
                '民宿', '宾馆', '招待所', '公寓酒店', '温泉酒店'
            ],
            '超市': [
                '大型超市', '便利店', '生鲜超市', '社区超市', '24小时便利店',
                '连锁超市', '精品超市', '进口超市', '有机超市', '折扣超市'
            ],
            '医院': [
                '综合医院', '专科医院', '中医院', '妇幼保健院', '口腔医院',
                '眼科医院', '骨科医院', '心血管医院', '肿瘤医院', '康复医院'
            ],
            '学校': [
                '小学', '中学', '高中', '职业学校', '培训学校',
                '幼儿园', '大学', '技术学院', '艺术学校', '体育学校'
            ]
        }
        
        # 获取对应关键词的模板
        templates = mock_templates.get(keyword, ['商店', '服务点', '营业场所'])
        
        # 生成模拟数据
        for i in range(min(limit, len(templates) * 2)):
            template = templates[i % len(templates)]
            
            poi = {
                'name': f"{region_name}{template}{i//len(templates)+1}号店",
                'display_name': f"{region_name}{template}{i//len(templates)+1}号店",
                'address': f"{region_name}市某某区某某街道{random.randint(1,999)}号",
                'phone': f"010-{random.randint(10000000, 99999999)}",
                'website': '',
                'category': keyword,
                'type': template,
                'lat': f"{random.uniform(39.8, 40.2):.6f}",  # 北京附近的纬度
                'lon': f"{random.uniform(116.2, 116.6):.6f}",  # 北京附近的经度
                'source': '模拟数据'
            }
            pois.append(poi)
        
        logger.info(f"生成了 {len(pois)} 个模拟POI数据")
        return pois

    def crawl_pois(self, region_code: str, keyword: str, output_file: str = None) -> List[Dict]:
        """
        综合爬取兴趣点
        """
        region_name = self.get_region_name(region_code)
        logger.info(f"开始爬取地区: {region_name} ({region_code}), 关键词: {keyword}")
        
        all_pois = []
        
        # 1. 尝试从Nominatim获取真实数据
        logger.info("正在从Nominatim/OSM获取数据...")
        try:
            nominatim_pois = self.crawl_nominatim_pois(region_name, keyword, 30)
            all_pois.extend(nominatim_pois)
            time.sleep(1)  # 遵守Nominatim的使用政策
        except Exception as e:
            logger.warning(f"Nominatim数据获取失败: {e}")
        
        # 2. 如果真实数据不足，补充模拟数据
        if len(all_pois) < 10:
            logger.info("真实数据不足，补充模拟数据...")
            mock_pois = self.crawl_mock_local_pois(region_name, keyword, 20)
            all_pois.extend(mock_pois)
        
        # 去重
        unique_pois = self.deduplicate_pois(all_pois)
        
        logger.info(f"总共获取到 {len(unique_pois)} 个去重后的POI")
        
        # 保存结果
        if output_file:
            self.save_to_csv(unique_pois, output_file)
            
        return unique_pois

    def deduplicate_pois(self, pois: List[Dict]) -> List[Dict]:
        """去重POI数据"""
        seen_names = set()
        unique_pois = []
        
        for poi in pois:
            name = poi.get('name', '').strip()
            if name and name not in seen_names:
                seen_names.add(name)
                unique_pois.append(poi)
                
        return unique_pois

    def save_to_csv(self, pois: List[Dict], filename: str):
        """保存POI数据到CSV文件"""
        if not pois:
            logger.warning("没有POI数据可保存")
            return
            
        fieldnames = ['name', 'display_name', 'address', 'phone', 'website', 
                     'category', 'type', 'lat', 'lon', 'source']
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(pois)
            
        logger.info(f"POI数据已保存到: {filename}")

    def list_supported_regions(self):
        """列出支持的地区代码"""
        print("支持的地区代码:")
        print("-" * 30)
        for code, name in self.region_codes.items():
            print(f"{code}: {name}")

    def list_supported_keywords(self):
        """列出支持的关键词"""
        keywords = {
            '餐饮': ['餐厅', '饭店', '小吃', '火锅', '烧烤', '快餐', '咖啡厅'],
            '住宿': ['酒店', '宾馆', '旅馆', '民宿', '青年旅社'],
            '购物': ['超市', '商场', '便利店', '专卖店', '市场'],
            '医疗': ['医院', '诊所', '药店', '牙科', '眼科'],
            '教育': ['学校', '幼儿园', '培训机构', '图书馆', '大学'],
            '交通': ['地铁站', '公交站', '火车站', '机场', '停车场'],
            '服务': ['银行', 'ATM', '邮局', '理发店', '美容院'],
            '娱乐': ['电影院', 'KTV', '游戏厅', '健身房', '公园']
        }
        
        print("支持的关键词分类:")
        print("-" * 30)
        for category, words in keywords.items():
            print(f"{category}: {', '.join(words)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简化版POI爬虫工具')
    parser.add_argument('--region', '-r', help='地区代码')
    parser.add_argument('--keyword', '-k', help='搜索关键词')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--list-regions', action='store_true', help='列出所有地区代码')
    parser.add_argument('--list-keywords', action='store_true', help='列出常用关键词')
    
    args = parser.parse_args()
    
    crawler = SimplePOICrawler()
    
    if args.list_regions:
        crawler.list_supported_regions()
        return
    
    if args.list_keywords:
        crawler.list_supported_keywords()
        return
    
    # 如果没有提供参数，使用默认值进行演示
    region_code = args.region or "110000"  # 默认北京
    keyword = args.keyword or "餐厅"       # 默认餐厅
    output_file = args.output or f"pois_{region_code}_{keyword}.csv"
    
    print(f"使用参数: 地区代码={region_code}, 关键词={keyword}")
    print("如需指定参数，请使用: python simple_poi_crawler.py -r 地区代码 -k 关键词")
    print("查看支持的地区: python simple_poi_crawler.py --list-regions")
    print("查看支持的关键词: python simple_poi_crawler.py --list-keywords")
    print("-" * 50)
    
    # 爬取POI
    pois = crawler.crawl_pois(region_code, keyword, output_file)
    
    # 打印结果
    print(f"\n获取到的POI列表 (共{len(pois)}个):")
    print("-" * 50)
    for i, poi in enumerate(pois[:10], 1):  # 只显示前10个
        print(f"{i:2d}. {poi['name']}")
        print(f"     地址: {poi['address']}")
        print(f"     电话: {poi['phone']}")
        print(f"     类型: {poi['type']}")
        print(f"     来源: {poi['source']}")
        if poi['lat'] and poi['lon']:
            print(f"     坐标: {poi['lat']}, {poi['lon']}")
        print()
    
    if len(pois) > 10:
        print(f"... 还有 {len(pois) - 10} 个POI，详细信息请查看输出文件")
    
    print(f"\n完整数据已保存到: {output_file}")

if __name__ == "__main__":
    main()
