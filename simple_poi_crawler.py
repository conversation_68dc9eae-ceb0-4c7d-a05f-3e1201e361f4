#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实POI爬虫 - 从实际网站爬取POI数据
"""

import requests
import json
import time
import csv
import re
import argparse
from urllib.parse import quote, urlencode
from bs4 import BeautifulSoup
import logging
from typing import List, Dict
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealPOICrawler:
    def __init__(self):
        self.session = requests.Session()
        # 轮换User-Agent
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]
        self.update_headers()

        # 地区代码映射
        self.region_codes = {
            '110000': '北京',
            '120000': '天津',
            '310000': '上海',
            '500000': '重庆',
            '130100': '石家庄',
            '140100': '太原',
            '210100': '沈阳',
            '220100': '长春',
            '230100': '哈尔滨',
            '320100': '南京',
            '330100': '杭州',
            '340100': '合肥',
            '350100': '福州',
            '360100': '南昌',
            '370100': '济南',
            '410100': '郑州',
            '420100': '武汉',
            '430100': '长沙',
            '440100': '广州',
            '440300': '深圳',
            '450100': '南宁',
            '460100': '海口',
            '510100': '成都',
            '520100': '贵阳',
            '530100': '昆明',
            '610100': '西安',
            '620100': '兰州',
            '630100': '西宁',
            '640100': '银川',
            '650100': '乌鲁木齐'
        }

    def update_headers(self):
        """更新请求头"""
        self.session.headers.update({
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        })

    def get_region_name(self, region_code: str) -> str:
        """根据地区代码获取地区名称"""
        return self.region_codes.get(region_code, region_code)

    def crawl_gaode_web_pois(self, region_name: str, keyword: str, limit: int = 50) -> List[Dict]:
        """
        从高德地图网页版爬取POI数据（不需要API key）
        """
        pois = []
        try:
            # 高德地图搜索页面
            search_url = "https://ditu.amap.com/search"
            params = {
                'query': keyword,
                'city': region_name,
                'geoobj': f'{region_name}|{region_name}',
                'zoom': '11'
            }

            self.update_headers()
            response = self.session.get(search_url, params=params, timeout=15)

            if response.status_code == 200:
                # 尝试从页面中提取POI数据
                soup = BeautifulSoup(response.text, 'html.parser')

                # 查找包含POI数据的script标签
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string and 'poilist' in script.string:
                        # 尝试提取JSON数据
                        try:
                            # 使用正则表达式提取POI数据
                            poi_pattern = r'"name":"([^"]+)".*?"address":"([^"]*)".*?"tel":"([^"]*)"'
                            matches = re.findall(poi_pattern, script.string)

                            for match in matches[:limit]:
                                name, address, phone = match
                                if name and name != keyword:  # 过滤掉搜索关键词本身
                                    poi = {
                                        'name': name,
                                        'display_name': name,
                                        'address': address,
                                        'phone': phone,
                                        'website': '',
                                        'category': keyword,
                                        'type': keyword,
                                        'lat': '',
                                        'lon': '',
                                        'source': '高德地图'
                                    }
                                    pois.append(poi)
                        except Exception as e:
                            logger.warning(f"解析高德数据失败: {e}")

            logger.info(f"从高德地图获取到 {len(pois)} 个POI")

        except Exception as e:
            logger.error(f"高德地图爬取失败: {e}")

        return pois

    def crawl_baidu_web_pois(self, region_name: str, keyword: str, limit: int = 50) -> List[Dict]:
        """
        从百度地图网页版爬取POI数据
        """
        pois = []
        try:
            # 百度地图搜索API（网页版）
            search_url = "https://map.baidu.com/"
            params = {
                'newmap': '1',
                'reqflag': 'pcmap',
                'biz': '1',
                'from': 'webmap',
                'da_par': 'direct',
                'pcevaname': 'pc4.1',
                'qt': 's',
                'da_src': 'searchBox.button',
                'wd': f'{region_name} {keyword}',
                'c': '1',
                'src': '0',
                'wd2': '',
                'pn': '0',
                'sug': '0',
                'l': '12',
                'b': '(12958376.09375,4825923.5;12959376.09375,4826923.5)',
                'from': 'webmap',
                'biz_forward': '{"scaler":1,"styles":"pl"}'
            }

            self.update_headers()
            response = self.session.get(search_url, params=params, timeout=15)

            if response.status_code == 200:
                # 尝试从响应中提取POI数据
                try:
                    # 百度地图返回的可能是JSON格式
                    if 'application/json' in response.headers.get('content-type', ''):
                        data = response.json()
                        if 'content' in data:
                            for item in data['content'][:limit]:
                                name = item.get('name', '')
                                address = item.get('addr', '')
                                phone = item.get('tel', '')

                                if name:
                                    poi = {
                                        'name': name,
                                        'display_name': name,
                                        'address': address,
                                        'phone': phone,
                                        'website': '',
                                        'category': keyword,
                                        'type': keyword,
                                        'lat': '',
                                        'lon': '',
                                        'source': '百度地图'
                                    }
                                    pois.append(poi)
                except Exception as e:
                    logger.warning(f"解析百度数据失败: {e}")

            logger.info(f"从百度地图获取到 {len(pois)} 个POI")

        except Exception as e:
            logger.error(f"百度地图爬取失败: {e}")

        return pois

    def crawl_dianping_pois(self, region_name: str, keyword: str, limit: int = 30) -> List[Dict]:
        """
        从大众点评爬取POI数据
        """
        pois = []
        try:
            # 大众点评搜索URL
            search_url = f"https://www.dianping.com/search/keyword/{quote(region_name)}/0_{quote(keyword)}"

            self.update_headers()
            # 添加大众点评特定的headers
            self.session.headers.update({
                'Referer': 'https://www.dianping.com/',
                'Host': 'www.dianping.com'
            })

            response = self.session.get(search_url, timeout=15)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # 多种选择器尝试
                selectors = [
                    'div.shop-item',
                    'li.shop-item',
                    'div.shop-list li',
                    'div.search-shop-list li',
                    '.shop-wrap',
                    '.shop-info'
                ]

                items = []
                for selector in selectors:
                    items = soup.select(selector)
                    if items:
                        logger.info(f"使用选择器 {selector} 找到 {len(items)} 个元素")
                        break

                for item in items[:limit]:
                    try:
                        # 提取店铺名称
                        name_selectors = ['h4', '.shop-name', '.title', 'a[title]', '.shop-title']
                        name = ''
                        for sel in name_selectors:
                            name_elem = item.select_one(sel)
                            if name_elem:
                                name = name_elem.get_text().strip()
                                if name:
                                    break

                        if not name:
                            continue

                        # 提取地址
                        addr_selectors = ['.addr', '.address', '.shop-addr', '.region']
                        address = ''
                        for sel in addr_selectors:
                            addr_elem = item.select_one(sel)
                            if addr_elem:
                                address = addr_elem.get_text().strip()
                                if address:
                                    break

                        # 提取电话
                        phone_selectors = ['.tel', '.phone', '.contact']
                        phone = ''
                        for sel in phone_selectors:
                            phone_elem = item.select_one(sel)
                            if phone_elem:
                                phone = phone_elem.get_text().strip()
                                if phone:
                                    break

                        poi = {
                            'name': name,
                            'display_name': name,
                            'address': address,
                            'phone': phone,
                            'website': '',
                            'category': keyword,
                            'type': keyword,
                            'lat': '',
                            'lon': '',
                            'source': '大众点评'
                        }
                        pois.append(poi)

                    except Exception as e:
                        logger.warning(f"解析大众点评项目失败: {e}")
                        continue

            logger.info(f"从大众点评获取到 {len(pois)} 个POI")

        except Exception as e:
            logger.error(f"大众点评爬取失败: {e}")

        return pois

    def crawl_58_pois(self, region_name: str, keyword: str, limit: int = 30) -> List[Dict]:
        """
        从58同城爬取POI数据
        """
        pois = []
        try:
            # 58同城搜索URL
            search_url = f"https://search.58.com/{quote(region_name)}/{quote(keyword)}/"

            self.update_headers()
            self.session.headers.update({
                'Referer': 'https://www.58.com/',
                'Host': 'search.58.com'
            })

            response = self.session.get(search_url, timeout=15)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # 58同城结果选择器
                selectors = [
                    'div.des',
                    'li.des',
                    '.list-item',
                    '.item-des',
                    '.search-item'
                ]

                items = []
                for selector in selectors:
                    items = soup.select(selector)
                    if items:
                        logger.info(f"使用选择器 {selector} 找到 {len(items)} 个元素")
                        break

                for item in items[:limit]:
                    try:
                        # 提取名称
                        name_selectors = ['h3 a', '.des-title a', '.title a', 'h4 a', '.item-title a']
                        name = ''
                        for sel in name_selectors:
                            name_elem = item.select_one(sel)
                            if name_elem:
                                name = name_elem.get_text().strip()
                                if name:
                                    break

                        if not name:
                            continue

                        # 提取地址
                        addr_selectors = ['.add', '.address', '.area', '.item-addr']
                        address = ''
                        for sel in addr_selectors:
                            addr_elem = item.select_one(sel)
                            if addr_elem:
                                address = addr_elem.get_text().strip()
                                if address:
                                    break

                        poi = {
                            'name': name,
                            'display_name': name,
                            'address': address,
                            'phone': '',
                            'website': '',
                            'category': keyword,
                            'type': keyword,
                            'lat': '',
                            'lon': '',
                            'source': '58同城'
                        }
                        pois.append(poi)

                    except Exception as e:
                        logger.warning(f"解析58同城项目失败: {e}")
                        continue

            logger.info(f"从58同城获取到 {len(pois)} 个POI")

        except Exception as e:
            logger.error(f"58同城爬取失败: {e}")

        return pois

    def crawl_pois(self, region_code: str, keyword: str, output_file: str = None) -> List[Dict]:
        """
        综合爬取兴趣点
        """
        region_name = self.get_region_name(region_code)
        logger.info(f"开始爬取地区: {region_name} ({region_code}), 关键词: {keyword}")

        all_pois = []

        # 1. 从高德地图爬取
        logger.info("正在从高德地图获取数据...")
        try:
            gaode_pois = self.crawl_gaode_web_pois(region_name, keyword, 20)
            all_pois.extend(gaode_pois)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"高德地图数据获取失败: {e}")

        # 2. 从百度地图爬取
        logger.info("正在从百度地图获取数据...")
        try:
            baidu_pois = self.crawl_baidu_web_pois(region_name, keyword, 20)
            all_pois.extend(baidu_pois)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"百度地图数据获取失败: {e}")

        # 3. 从大众点评爬取
        logger.info("正在从大众点评获取数据...")
        try:
            dianping_pois = self.crawl_dianping_pois(region_name, keyword, 15)
            all_pois.extend(dianping_pois)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"大众点评数据获取失败: {e}")

        # 4. 从58同城爬取
        logger.info("正在从58同城获取数据...")
        try:
            pois_58 = self.crawl_58_pois(region_name, keyword, 15)
            all_pois.extend(pois_58)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"58同城数据获取失败: {e}")

        # 去重和清理
        unique_pois = self.deduplicate_pois(all_pois)
        cleaned_pois = self.clean_pois(unique_pois)

        logger.info(f"总共获取到 {len(cleaned_pois)} 个去重清理后的POI")

        # 保存结果
        if output_file:
            self.save_to_csv(cleaned_pois, output_file)

        return cleaned_pois

    def clean_pois(self, pois: List[Dict]) -> List[Dict]:
        """清理POI数据"""
        cleaned_pois = []

        for poi in pois:
            # 清理名称
            name = poi.get('name', '').strip()
            if not name or len(name) < 2:
                continue

            # 过滤掉明显的垃圾数据
            if any(x in name.lower() for x in ['广告', '推广', '置顶', '热门']):
                continue

            # 清理电话号码
            phone = poi.get('phone', '').strip()
            phone = re.sub(r'[^\d\-\+\(\)\s]', '', phone)

            # 清理地址
            address = poi.get('address', '').strip()

            poi.update({
                'name': name,
                'phone': phone,
                'address': address
            })

            cleaned_pois.append(poi)

        return cleaned_pois

    def deduplicate_pois(self, pois: List[Dict]) -> List[Dict]:
        """去重POI数据"""
        seen_names = set()
        unique_pois = []
        
        for poi in pois:
            name = poi.get('name', '').strip()
            if name and name not in seen_names:
                seen_names.add(name)
                unique_pois.append(poi)
                
        return unique_pois

    def save_to_csv(self, pois: List[Dict], filename: str):
        """保存POI数据到CSV文件"""
        if not pois:
            logger.warning("没有POI数据可保存")
            return
            
        fieldnames = ['name', 'display_name', 'address', 'phone', 'website', 
                     'category', 'type', 'lat', 'lon', 'source']
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(pois)
            
        logger.info(f"POI数据已保存到: {filename}")

    def list_supported_regions(self):
        """列出支持的地区代码"""
        print("支持的地区代码:")
        print("-" * 30)
        for code, name in self.region_codes.items():
            print(f"{code}: {name}")

    def list_supported_keywords(self):
        """列出支持的关键词"""
        keywords = {
            '餐饮': ['餐厅', '饭店', '小吃', '火锅', '烧烤', '快餐', '咖啡厅'],
            '住宿': ['酒店', '宾馆', '旅馆', '民宿', '青年旅社'],
            '购物': ['超市', '商场', '便利店', '专卖店', '市场'],
            '医疗': ['医院', '诊所', '药店', '牙科', '眼科'],
            '教育': ['学校', '幼儿园', '培训机构', '图书馆', '大学'],
            '交通': ['地铁站', '公交站', '火车站', '机场', '停车场'],
            '服务': ['银行', 'ATM', '邮局', '理发店', '美容院'],
            '娱乐': ['电影院', 'KTV', '游戏厅', '健身房', '公园']
        }

        print("支持的关键词分类:")
        print("-" * 30)
        for category, words in keywords.items():
            print(f"{category}: {', '.join(words)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='真实POI爬虫工具')
    parser.add_argument('--region', '-r', help='地区代码')
    parser.add_argument('--keyword', '-k', help='搜索关键词')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--list-regions', action='store_true', help='列出所有地区代码')
    parser.add_argument('--list-keywords', action='store_true', help='列出常用关键词')

    args = parser.parse_args()

    crawler = RealPOICrawler()

    if args.list_regions:
        crawler.list_supported_regions()
        return

    if args.list_keywords:
        crawler.list_supported_keywords()
        return

    # 如果没有提供参数，使用默认值进行演示
    region_code = args.region or "110000"  # 默认北京
    keyword = args.keyword or "餐厅"       # 默认餐厅
    output_file = args.output or f"real_pois_{region_code}_{keyword}.csv"

    print(f"使用参数: 地区代码={region_code}, 关键词={keyword}")
    print("如需指定参数，请使用: python simple_poi_crawler.py -r 地区代码 -k 关键词")
    print("查看支持的地区: python simple_poi_crawler.py --list-regions")
    print("查看支持的关键词: python simple_poi_crawler.py --list-keywords")
    print("-" * 50)

    # 爬取POI
    pois = crawler.crawl_pois(region_code, keyword, output_file)

    # 打印结果
    print(f"\n获取到的POI列表 (共{len(pois)}个):")
    print("-" * 50)
    for i, poi in enumerate(pois[:10], 1):  # 只显示前10个
        print(f"{i:2d}. {poi['name']}")
        print(f"     地址: {poi['address']}")
        print(f"     电话: {poi['phone']}")
        print(f"     类型: {poi['type']}")
        print(f"     来源: {poi['source']}")
        if poi['lat'] and poi['lon']:
            print(f"     坐标: {poi['lat']}, {poi['lon']}")
        print()

    if len(pois) > 10:
        print(f"... 还有 {len(pois) - 10} 个POI，详细信息请查看输出文件")

    print(f"\n完整数据已保存到: {output_file}")

if __name__ == "__main__":
    main()
