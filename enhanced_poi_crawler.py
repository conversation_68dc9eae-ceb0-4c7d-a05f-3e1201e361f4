#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版POI爬虫
支持多数据源、批量爬取、结果去重等功能
"""

import requests
import json
import time
import csv
import re
import argparse
from urllib.parse import quote, urljoin
from bs4 import BeautifulSoup
import logging
from typing import List, Dict, Optional
import random
from config import REGION_CODES, KEYWORD_CATEGORIES, CRAWLER_CONFIG, OUTPUT_CONFIG
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('poi_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedPOICrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        self.region_codes = REGION_CODES
        self.config = CRAWLER_CONFIG

    def get_region_name(self, region_code: str) -> str:
        """根据地区代码获取地区名称"""
        return self.region_codes.get(region_code, region_code)

    def crawl_osm_pois(self, region_name: str, keyword: str, limit: int = None) -> List[Dict]:
        """从OpenStreetMap爬取兴趣点"""
        if not self.config.get('enable_osm', True):
            return []
            
        limit = limit or self.config.get('max_pois_per_source', 100)
        pois = []
        
        try:
            # 多个Overpass API服务器
            overpass_servers = [
                "http://overpass-api.de/api/interpreter",
                "http://overpass.kumi.systems/api/interpreter",
                "https://overpass.openstreetmap.ru/api/interpreter"
            ]
            
            # 构建更精确的查询语句
            query = f"""
            [out:json][timeout:30];
            (
              node["name"~"{keyword}",i](area.searchArea);
              way["name"~"{keyword}",i](area.searchArea);
              relation["name"~"{keyword}",i](area.searchArea);
            );
            (._;>;);
            out center meta;
            """
            
            # 尝试不同的服务器
            for server in overpass_servers:
                try:
                    response = self.session.post(
                        server, 
                        data={'data': query}, 
                        timeout=self.config.get('request_timeout', 15)
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        for element in data.get('elements', [])[:limit]:
                            tags = element.get('tags', {})
                            name = tags.get('name', '')
                            
                            if not name:
                                continue
                                
                            poi = {
                                'name': name,
                                'address': self._extract_address(tags),
                                'phone': tags.get('phone', ''),
                                'website': tags.get('website', ''),
                                'amenity': tags.get('amenity', ''),
                                'shop': tags.get('shop', ''),
                                'cuisine': tags.get('cuisine', ''),
                                'opening_hours': tags.get('opening_hours', ''),
                                'lat': element.get('lat') or element.get('center', {}).get('lat', ''),
                                'lon': element.get('lon') or element.get('center', {}).get('lon', ''),
                                'source': 'OpenStreetMap',
                                'osm_id': element.get('id', ''),
                                'osm_type': element.get('type', '')
                            }
                            pois.append(poi)
                            
                        logger.info(f"从OSM获取到 {len(pois)} 个POI")
                        break
                        
                except Exception as e:
                    logger.warning(f"OSM服务器 {server} 请求失败: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"OSM爬取失败: {e}")
            
        return pois

    def _extract_address(self, tags: Dict) -> str:
        """从OSM标签中提取地址"""
        address_parts = []
        
        # 按优先级提取地址组件
        addr_components = [
            'addr:full',
            'addr:street', 
            'addr:housenumber',
            'addr:city',
            'addr:district',
            'addr:province'
        ]
        
        for component in addr_components:
            if component in tags:
                address_parts.append(tags[component])
                
        return ' '.join(address_parts) if address_parts else ''

    def crawl_web_pois(self, region_name: str, keyword: str, source: str = 'dianping') -> List[Dict]:
        """通用网站POI爬取方法"""
        pois = []
        
        try:
            if source == 'dianping':
                search_url = f"https://www.dianping.com/search/keyword/{quote(region_name)}/0_{quote(keyword)}"
                pois = self._parse_dianping_results(search_url, keyword)
            elif source == '58':
                search_url = f"https://search.58.com/{quote(region_name)}/{quote(keyword)}/"
                pois = self._parse_58_results(search_url, keyword)
            elif source == 'meituan':
                search_url = f"https://www.meituan.com/s/{quote(keyword)}?region={quote(region_name)}"
                pois = self._parse_meituan_results(search_url, keyword)
                
        except Exception as e:
            logger.error(f"{source}爬取失败: {e}")
            
        return pois

    def _parse_dianping_results(self, url: str, keyword: str) -> List[Dict]:
        """解析大众点评搜索结果"""
        pois = []
        
        try:
            response = self.session.get(url, timeout=self.config.get('request_timeout', 15))
            if response.status_code != 200:
                return pois
                
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 多种选择器尝试
            selectors = [
                'div.shop-item',
                'div.shop-list li',
                'div.search-shop-list li'
            ]
            
            items = []
            for selector in selectors:
                items = soup.select(selector)
                if items:
                    break
                    
            for item in items[:self.config.get('max_pois_per_source', 50)]:
                try:
                    # 提取店铺名称
                    name_selectors = ['h4', '.shop-name', '.title', 'a[title]']
                    name = ''
                    for sel in name_selectors:
                        name_elem = item.select_one(sel)
                        if name_elem:
                            name = name_elem.get_text().strip()
                            break
                    
                    if not name:
                        continue
                        
                    # 提取地址
                    addr_selectors = ['.addr', '.address', '.shop-addr']
                    address = ''
                    for sel in addr_selectors:
                        addr_elem = item.select_one(sel)
                        if addr_elem:
                            address = addr_elem.get_text().strip()
                            break
                    
                    # 提取电话
                    phone_selectors = ['.tel', '.phone', '.contact']
                    phone = ''
                    for sel in phone_selectors:
                        phone_elem = item.select_one(sel)
                        if phone_elem:
                            phone = phone_elem.get_text().strip()
                            break
                    
                    poi = {
                        'name': name,
                        'address': address,
                        'phone': phone,
                        'website': '',
                        'amenity': keyword,
                        'shop': '',
                        'cuisine': '',
                        'opening_hours': '',
                        'lat': '',
                        'lon': '',
                        'source': '大众点评',
                        'osm_id': '',
                        'osm_type': ''
                    }
                    pois.append(poi)
                    
                except Exception as e:
                    logger.warning(f"解析大众点评项目失败: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"大众点评页面解析失败: {e}")
            
        return pois

    def _parse_58_results(self, url: str, keyword: str) -> List[Dict]:
        """解析58同城搜索结果"""
        pois = []
        
        try:
            response = self.session.get(url, timeout=self.config.get('request_timeout', 15))
            if response.status_code != 200:
                return pois
                
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 58同城结果选择器
            items = soup.select('div.des, li.des, .list-item')
            
            for item in items[:self.config.get('max_pois_per_source', 30)]:
                try:
                    # 提取名称
                    name_elem = item.select_one('h3 a, .des-title a, .title a')
                    name = name_elem.get_text().strip() if name_elem else ''
                    
                    if not name:
                        continue
                        
                    # 提取地址
                    addr_elem = item.select_one('.add, .address, .area')
                    address = addr_elem.get_text().strip() if addr_elem else ''
                    
                    poi = {
                        'name': name,
                        'address': address,
                        'phone': '',
                        'website': '',
                        'amenity': keyword,
                        'shop': '',
                        'cuisine': '',
                        'opening_hours': '',
                        'lat': '',
                        'lon': '',
                        'source': '58同城',
                        'osm_id': '',
                        'osm_type': ''
                    }
                    pois.append(poi)
                    
                except Exception as e:
                    logger.warning(f"解析58同城项目失败: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"58同城页面解析失败: {e}")
            
        return pois

    def _parse_meituan_results(self, url: str, keyword: str) -> List[Dict]:
        """解析美团搜索结果"""
        pois = []
        
        try:
            response = self.session.get(url, timeout=self.config.get('request_timeout', 15))
            if response.status_code != 200:
                return pois
                
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 美团结果选择器
            items = soup.select('.poi-item, .shop-item, .search-item')
            
            for item in items[:self.config.get('max_pois_per_source', 30)]:
                try:
                    name_elem = item.select_one('.poi-name, .shop-name, .title')
                    name = name_elem.get_text().strip() if name_elem else ''
                    
                    if not name:
                        continue
                        
                    addr_elem = item.select_one('.poi-addr, .address')
                    address = addr_elem.get_text().strip() if addr_elem else ''
                    
                    poi = {
                        'name': name,
                        'address': address,
                        'phone': '',
                        'website': '',
                        'amenity': keyword,
                        'shop': '',
                        'cuisine': '',
                        'opening_hours': '',
                        'lat': '',
                        'lon': '',
                        'source': '美团',
                        'osm_id': '',
                        'osm_type': ''
                    }
                    pois.append(poi)
                    
                except Exception as e:
                    logger.warning(f"解析美团项目失败: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"美团页面解析失败: {e}")
            
        return pois

    def crawl_pois(self, region_code: str, keyword: str, output_file: str = None) -> List[Dict]:
        """综合爬取兴趣点"""
        region_name = self.get_region_name(region_code)
        logger.info(f"开始爬取地区: {region_name} ({region_code}), 关键词: {keyword}")
        
        all_pois = []
        
        # 并发爬取不同数据源
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = []
            
            # OSM
            if self.config.get('enable_osm', True):
                futures.append(executor.submit(self.crawl_osm_pois, region_name, keyword))
            
            # 大众点评
            if self.config.get('enable_dianping', True):
                futures.append(executor.submit(self.crawl_web_pois, region_name, keyword, 'dianping'))
            
            # 58同城
            if self.config.get('enable_58', True):
                futures.append(executor.submit(self.crawl_web_pois, region_name, keyword, '58'))
            
            # 美团
            futures.append(executor.submit(self.crawl_web_pois, region_name, keyword, 'meituan'))
            
            # 收集结果
            for future in as_completed(futures):
                try:
                    pois = future.result()
                    all_pois.extend(pois)
                    time.sleep(random.uniform(*self.config.get('delay_range', (1, 3))))
                except Exception as e:
                    logger.error(f"数据源爬取失败: {e}")
        
        # 去重和清理
        unique_pois = self.deduplicate_pois(all_pois)
        cleaned_pois = self.clean_pois(unique_pois)
        
        logger.info(f"总共获取到 {len(cleaned_pois)} 个去重清理后的POI")
        
        # 保存结果
        if output_file:
            self.save_to_csv(cleaned_pois, output_file)
            
        return cleaned_pois

    def deduplicate_pois(self, pois: List[Dict]) -> List[Dict]:
        """去重POI数据"""
        seen = set()
        unique_pois = []
        
        for poi in pois:
            # 使用名称和地址的组合作为去重键
            name = poi.get('name', '').strip()
            address = poi.get('address', '').strip()
            key = f"{name}_{address}"
            
            if key and key not in seen:
                seen.add(key)
                unique_pois.append(poi)
                
        return unique_pois

    def clean_pois(self, pois: List[Dict]) -> List[Dict]:
        """清理POI数据"""
        cleaned_pois = []
        
        for poi in pois:
            # 清理名称
            name = poi.get('name', '').strip()
            if not name or len(name) < 2:
                continue
                
            # 清理电话号码
            phone = poi.get('phone', '').strip()
            phone = re.sub(r'[^\d\-\+\(\)\s]', '', phone)
            
            # 清理地址
            address = poi.get('address', '').strip()
            
            poi.update({
                'name': name,
                'phone': phone,
                'address': address
            })
            
            cleaned_pois.append(poi)
            
        return cleaned_pois

    def save_to_csv(self, pois: List[Dict], filename: str):
        """保存POI数据到CSV文件"""
        if not pois:
            logger.warning("没有POI数据可保存")
            return
            
        # 确保输出目录存在
        os.makedirs(os.path.dirname(filename) if os.path.dirname(filename) else '.', exist_ok=True)
        
        fieldnames = [
            'name', 'address', 'phone', 'website', 'amenity', 'shop', 
            'cuisine', 'opening_hours', 'lat', 'lon', 'source', 'osm_id', 'osm_type'
        ]
        
        with open(filename, 'w', newline='', encoding=OUTPUT_CONFIG.get('csv_encoding', 'utf-8')) as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(pois)
            
        logger.info(f"POI数据已保存到: {filename}")

    def batch_crawl(self, region_codes: List[str], keywords: List[str], output_dir: str = 'output'):
        """批量爬取多个地区和关键词的POI"""
        os.makedirs(output_dir, exist_ok=True)
        
        total_tasks = len(region_codes) * len(keywords)
        current_task = 0
        
        for region_code in region_codes:
            for keyword in keywords:
                current_task += 1
                logger.info(f"进度: {current_task}/{total_tasks} - 爬取 {region_code} {keyword}")
                
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = os.path.join(output_dir, f"pois_{region_code}_{keyword}_{timestamp}.csv")
                
                try:
                    pois = self.crawl_pois(region_code, keyword, output_file)
                    logger.info(f"完成: {region_code} {keyword} - 获取 {len(pois)} 个POI")
                except Exception as e:
                    logger.error(f"批量爬取失败: {region_code} {keyword} - {e}")
                
                # 任务间延时
                time.sleep(random.uniform(2, 5))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='POI爬虫工具')
    parser.add_argument('--region', '-r', required=True, help='地区代码')
    parser.add_argument('--keyword', '-k', required=True, help='搜索关键词')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--batch', action='store_true', help='批量模式')
    parser.add_argument('--list-regions', action='store_true', help='列出所有地区代码')
    parser.add_argument('--list-keywords', action='store_true', help='列出常用关键词')
    
    args = parser.parse_args()
    
    crawler = EnhancedPOICrawler()
    
    if args.list_regions:
        print("地区代码列表:")
        for code, name in REGION_CODES.items():
            print(f"{code}: {name}")
        return
    
    if args.list_keywords:
        print("常用关键词分类:")
        for category, keywords in KEYWORD_CATEGORIES.items():
            print(f"{category}: {', '.join(keywords)}")
        return
    
    if args.batch:
        # 批量模式示例
        region_codes = ['110000', '310000']  # 北京、上海
        keywords = ['餐厅', '酒店', '超市']
        crawler.batch_crawl(region_codes, keywords)
    else:
        # 单次爬取
        output_file = args.output or f"pois_{args.region}_{args.keyword}.csv"
        pois = crawler.crawl_pois(args.region, args.keyword, output_file)
        
        # 显示前几个结果
        print(f"\n获取到的前5个POI:")
        for i, poi in enumerate(pois[:5], 1):
            print(f"{i}. {poi['name']} - {poi['address']} ({poi['source']})")

if __name__ == "__main__":
    main()
