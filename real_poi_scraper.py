#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实POI数据爬虫 - 从实际可用的数据源获取POI
"""

import requests
import json
import time
import csv
import re
import argparse
from urllib.parse import quote, urlencode
from bs4 import BeautifulSoup
import logging
from typing import List, Dict
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealPOIScraper:
    def __init__(self):
        self.session = requests.Session()
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]
        self.update_headers()
        
        # 地区代码映射
        self.region_codes = {
            '110000': '北京',
            '120000': '天津',
            '310000': '上海',
            '500000': '重庆',
            '440100': '广州',
            '440300': '深圳',
            '330100': '杭州',
            '320100': '南京',
            '510100': '成都',
            '420100': '武汉',
            '610100': '西安',
            '430100': '长沙'
        }

    def update_headers(self):
        """更新请求头"""
        self.session.headers.update({
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })

    def get_region_name(self, region_code: str) -> str:
        """根据地区代码获取地区名称"""
        return self.region_codes.get(region_code, region_code)

    def scrape_openstreetmap_overpass(self, region_name: str, keyword: str, limit: int = 50) -> List[Dict]:
        """
        使用Overpass API从OpenStreetMap获取POI数据
        """
        pois = []
        try:
            # 构建Overpass查询
            overpass_url = "https://overpass-api.de/api/interpreter"
            
            # 根据关键词构建查询
            amenity_map = {
                '餐厅': 'restaurant',
                '酒店': 'hotel',
                '超市': 'supermarket',
                '医院': 'hospital',
                '学校': 'school',
                '银行': 'bank',
                '加油站': 'fuel'
            }
            
            amenity = amenity_map.get(keyword, 'restaurant')
            
            query = f"""
            [out:json][timeout:25];
            (
              node["amenity"="{amenity}"]["addr:city"~"{region_name}",i];
              way["amenity"="{amenity}"]["addr:city"~"{region_name}",i];
              relation["amenity"="{amenity}"]["addr:city"~"{region_name}",i];
            );
            out center meta;
            """
            
            response = self.session.post(overpass_url, data={'data': query}, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                for element in data.get('elements', [])[:limit]:
                    tags = element.get('tags', {})
                    name = tags.get('name', '')
                    
                    if name:
                        poi = {
                            'name': name,
                            'address': self._extract_osm_address(tags),
                            'phone': tags.get('phone', ''),
                            'website': tags.get('website', ''),
                            'category': keyword,
                            'type': amenity,
                            'lat': element.get('lat') or element.get('center', {}).get('lat', ''),
                            'lon': element.get('lon') or element.get('center', {}).get('lon', ''),
                            'source': 'OpenStreetMap'
                        }
                        pois.append(poi)
                        
            logger.info(f"从OpenStreetMap获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"OpenStreetMap爬取失败: {e}")
            
        return pois

    def _extract_osm_address(self, tags: Dict) -> str:
        """从OSM标签中提取地址"""
        address_parts = []
        
        # 按优先级提取地址组件
        addr_components = [
            'addr:full',
            'addr:street', 
            'addr:housenumber',
            'addr:district',
            'addr:city'
        ]
        
        for component in addr_components:
            if component in tags:
                address_parts.append(tags[component])
                
        return ' '.join(address_parts) if address_parts else ''

    def scrape_amap_search(self, region_name: str, keyword: str, limit: int = 30) -> List[Dict]:
        """
        通过高德地图搜索接口获取POI（模拟网页搜索）
        """
        pois = []
        try:
            # 高德地图搜索接口
            search_url = "https://restapi.amap.com/v3/place/text"
            
            # 这里使用公开的测试key，实际使用时应该申请自己的key
            params = {
                'key': 'your_amap_key_here',  # 需要替换为实际的key
                'keywords': keyword,
                'city': region_name,
                'output': 'json',
                'offset': limit,
                'page': 1,
                'extensions': 'all'
            }
            
            # 由于没有真实的API key，这里改为爬取网页版
            web_url = f"https://ditu.amap.com/search?query={quote(keyword)}&city={quote(region_name)}"
            
            self.update_headers()
            response = self.session.get(web_url, timeout=15)
            
            if response.status_code == 200:
                # 尝试从页面中提取POI信息
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找可能包含POI数据的元素
                poi_elements = soup.find_all(['div', 'li'], class_=re.compile(r'poi|item|result'))
                
                for element in poi_elements[:limit]:
                    try:
                        name_elem = element.find(['h3', 'h4', 'span'], class_=re.compile(r'name|title'))
                        if name_elem:
                            name = name_elem.get_text().strip()
                            
                            addr_elem = element.find(['span', 'div'], class_=re.compile(r'addr|address'))
                            address = addr_elem.get_text().strip() if addr_elem else ''
                            
                            if name and len(name) > 1:
                                poi = {
                                    'name': name,
                                    'address': address,
                                    'phone': '',
                                    'website': '',
                                    'category': keyword,
                                    'type': keyword,
                                    'lat': '',
                                    'lon': '',
                                    'source': '高德地图'
                                }
                                pois.append(poi)
                    except Exception as e:
                        continue
                        
            logger.info(f"从高德地图获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"高德地图爬取失败: {e}")
            
        return pois

    def scrape_meituan_pois(self, region_name: str, keyword: str, limit: int = 30) -> List[Dict]:
        """
        从美团网爬取POI数据
        """
        pois = []
        try:
            # 美团搜索URL
            search_url = f"https://www.meituan.com/s/{quote(keyword)}"
            
            self.update_headers()
            self.session.headers.update({
                'Referer': 'https://www.meituan.com/',
                'Host': 'www.meituan.com'
            })
            
            params = {
                'region': region_name,
                'q': keyword
            }
            
            response = self.session.get(search_url, params=params, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找商家信息
                shop_items = soup.find_all(['div', 'li'], class_=re.compile(r'shop|poi|item'))
                
                for item in shop_items[:limit]:
                    try:
                        # 提取店名
                        name_elem = item.find(['h3', 'h4', 'a'], class_=re.compile(r'name|title'))
                        if not name_elem:
                            name_elem = item.find('a', title=True)
                        
                        if name_elem:
                            name = name_elem.get_text().strip() or name_elem.get('title', '').strip()
                            
                            # 提取地址
                            addr_elem = item.find(['span', 'div'], class_=re.compile(r'addr|address|location'))
                            address = addr_elem.get_text().strip() if addr_elem else ''
                            
                            if name and len(name) > 1:
                                poi = {
                                    'name': name,
                                    'address': address,
                                    'phone': '',
                                    'website': '',
                                    'category': keyword,
                                    'type': keyword,
                                    'lat': '',
                                    'lon': '',
                                    'source': '美团'
                                }
                                pois.append(poi)
                                
                    except Exception as e:
                        continue
                        
            logger.info(f"从美团获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"美团爬取失败: {e}")
            
        return pois

    def scrape_pois(self, region_code: str, keyword: str, output_file: str = None) -> List[Dict]:
        """
        综合爬取POI数据
        """
        region_name = self.get_region_name(region_code)
        logger.info(f"开始爬取地区: {region_name} ({region_code}), 关键词: {keyword}")
        
        all_pois = []
        
        # 1. 从OpenStreetMap获取数据
        logger.info("正在从OpenStreetMap获取数据...")
        try:
            osm_pois = self.scrape_openstreetmap_overpass(region_name, keyword, 20)
            all_pois.extend(osm_pois)
            time.sleep(2)
        except Exception as e:
            logger.warning(f"OpenStreetMap数据获取失败: {e}")
        
        # 2. 从高德地图获取数据
        logger.info("正在从高德地图获取数据...")
        try:
            amap_pois = self.scrape_amap_search(region_name, keyword, 15)
            all_pois.extend(amap_pois)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"高德地图数据获取失败: {e}")
        
        # 3. 从美团获取数据
        logger.info("正在从美团获取数据...")
        try:
            meituan_pois = self.scrape_meituan_pois(region_name, keyword, 15)
            all_pois.extend(meituan_pois)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"美团数据获取失败: {e}")
        
        # 去重和清理
        unique_pois = self.deduplicate_pois(all_pois)
        cleaned_pois = self.clean_pois(unique_pois)
        
        logger.info(f"总共获取到 {len(cleaned_pois)} 个去重清理后的POI")
        
        # 保存结果
        if output_file:
            self.save_to_csv(cleaned_pois, output_file)
            
        return cleaned_pois

    def deduplicate_pois(self, pois: List[Dict]) -> List[Dict]:
        """去重POI数据"""
        seen = set()
        unique_pois = []
        
        for poi in pois:
            # 使用名称和地址的组合作为去重键
            name = poi.get('name', '').strip()
            address = poi.get('address', '').strip()
            key = f"{name}_{address[:20]}"  # 只取地址前20个字符避免过长
            
            if key and key not in seen and name:
                seen.add(key)
                unique_pois.append(poi)
                
        return unique_pois

    def clean_pois(self, pois: List[Dict]) -> List[Dict]:
        """清理POI数据"""
        cleaned_pois = []
        
        for poi in pois:
            # 清理名称
            name = poi.get('name', '').strip()
            if not name or len(name) < 2:
                continue
                
            # 过滤掉明显的垃圾数据
            if any(x in name.lower() for x in ['广告', '推广', '置顶', '热门', 'ad', 'advertisement']):
                continue
                
            # 清理电话号码
            phone = poi.get('phone', '').strip()
            phone = re.sub(r'[^\d\-\+\(\)\s]', '', phone)
            
            # 清理地址
            address = poi.get('address', '').strip()
            
            poi.update({
                'name': name,
                'phone': phone,
                'address': address
            })
            
            cleaned_pois.append(poi)
            
        return cleaned_pois

    def save_to_csv(self, pois: List[Dict], filename: str):
        """保存POI数据到CSV文件"""
        if not pois:
            logger.warning("没有POI数据可保存")
            return
            
        fieldnames = ['name', 'address', 'phone', 'website', 'category', 'type', 'lat', 'lon', 'source']
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(pois)
            
        logger.info(f"POI数据已保存到: {filename}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='真实POI数据爬虫')
    parser.add_argument('--region', '-r', required=True, help='地区代码')
    parser.add_argument('--keyword', '-k', required=True, help='搜索关键词')
    parser.add_argument('--output', '-o', help='输出文件路径')
    
    args = parser.parse_args()
    
    scraper = RealPOIScraper()
    
    output_file = args.output or f"real_pois_{args.region}_{args.keyword}.csv"
    
    print(f"开始爬取 {scraper.get_region_name(args.region)} 的 {args.keyword} 数据...")
    print("-" * 50)
    
    # 爬取POI
    pois = scraper.scrape_pois(args.region, args.keyword, output_file)
    
    # 显示结果
    if pois:
        print(f"\n成功获取到 {len(pois)} 个POI:")
        print("-" * 50)
        
        # 按数据源统计
        sources = {}
        for poi in pois:
            source = poi.get('source', '未知')
            sources[source] = sources.get(source, 0) + 1
        
        print("数据源统计:")
        for source, count in sources.items():
            print(f"  {source}: {count} 个")
        
        print(f"\n前10个POI:")
        for i, poi in enumerate(pois[:10], 1):
            print(f"{i:2d}. {poi['name']}")
            if poi['address']:
                print(f"     地址: {poi['address']}")
            if poi['phone']:
                print(f"     电话: {poi['phone']}")
            print(f"     来源: {poi['source']}")
            print()
            
        print(f"完整数据已保存到: {output_file}")
    else:
        print("未获取到任何POI数据，请检查网络连接或尝试其他关键词")

if __name__ == "__main__":
    main()
