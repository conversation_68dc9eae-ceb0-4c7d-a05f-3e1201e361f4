#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兴趣点(POI)爬取脚本
支持按地区代码和关键词爬取兴趣点信息
数据源：OpenStreetMap、大众点评、58同城等
"""

import requests
import json
import time
import csv
import re
from urllib.parse import quote, urljoin
from bs4 import BeautifulSoup
import logging
from typing import List, Dict, Optional
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class POICrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 地区代码映射
        self.region_codes = {
            '110000': '北京市',
            '120000': '天津市', 
            '130000': '河北省',
            '140000': '山西省',
            '150000': '内蒙古自治区',
            '210000': '辽宁省',
            '220000': '吉林省',
            '230000': '黑龙江省',
            '310000': '上海市',
            '320000': '江苏省',
            '330000': '浙江省',
            '340000': '安徽省',
            '350000': '福建省',
            '360000': '江西省',
            '370000': '山东省',
            '410000': '河南省',
            '420000': '湖北省',
            '430000': '湖南省',
            '440000': '广东省',
            '450000': '广西壮族自治区',
            '460000': '海南省',
            '500000': '重庆市',
            '510000': '四川省',
            '520000': '贵州省',
            '530000': '云南省',
            '540000': '西藏自治区',
            '610000': '陕西省',
            '620000': '甘肃省',
            '630000': '青海省',
            '640000': '宁夏回族自治区',
            '650000': '新疆维吾尔自治区'
        }

    def get_region_name(self, region_code: str) -> str:
        """根据地区代码获取地区名称"""
        return self.region_codes.get(region_code, region_code)

    def crawl_osm_pois(self, region_name: str, keyword: str, limit: int = 100) -> List[Dict]:
        """
        从OpenStreetMap爬取兴趣点
        使用Overpass API查询OSM数据
        """
        pois = []
        try:
            # Overpass API查询语句
            overpass_url = "http://overpass-api.de/api/interpreter"
            
            # 构建查询语句
            query = f"""
            [out:json][timeout:25];
            (
              node["name"~"{keyword}",i]["addr:city"~"{region_name}",i];
              way["name"~"{keyword}",i]["addr:city"~"{region_name}",i];
              relation["name"~"{keyword}",i]["addr:city"~"{region_name}",i];
            );
            out center meta;
            """
            
            response = self.session.post(overpass_url, data={'data': query}, timeout=30)
            if response.status_code == 200:
                data = response.json()
                
                for element in data.get('elements', [])[:limit]:
                    poi = {
                        'name': element.get('tags', {}).get('name', ''),
                        'address': element.get('tags', {}).get('addr:full', ''),
                        'phone': element.get('tags', {}).get('phone', ''),
                        'website': element.get('tags', {}).get('website', ''),
                        'amenity': element.get('tags', {}).get('amenity', ''),
                        'shop': element.get('tags', {}).get('shop', ''),
                        'lat': element.get('lat') or element.get('center', {}).get('lat'),
                        'lon': element.get('lon') or element.get('center', {}).get('lon'),
                        'source': 'OpenStreetMap'
                    }
                    
                    if poi['name']:  # 只保留有名称的POI
                        pois.append(poi)
                        
            logger.info(f"从OSM获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"OSM爬取失败: {e}")
            
        return pois

    def crawl_dianping_pois(self, region_name: str, keyword: str, limit: int = 50) -> List[Dict]:
        """
        从大众点评爬取兴趣点（模拟搜索）
        """
        pois = []
        try:
            # 大众点评搜索URL
            search_url = f"https://www.dianping.com/search/keyword/{quote(region_name)}/0_{quote(keyword)}"
            
            response = self.session.get(search_url, timeout=15)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 解析搜索结果
                shop_items = soup.find_all('div', class_='shop-item')
                
                for item in shop_items[:limit]:
                    try:
                        name_elem = item.find('h4')
                        name = name_elem.get_text().strip() if name_elem else ''
                        
                        address_elem = item.find('span', class_='addr')
                        address = address_elem.get_text().strip() if address_elem else ''
                        
                        phone_elem = item.find('span', class_='tel')
                        phone = phone_elem.get_text().strip() if phone_elem else ''
                        
                        if name:
                            poi = {
                                'name': name,
                                'address': address,
                                'phone': phone,
                                'website': '',
                                'amenity': keyword,
                                'shop': '',
                                'lat': '',
                                'lon': '',
                                'source': '大众点评'
                            }
                            pois.append(poi)
                            
                    except Exception as e:
                        logger.warning(f"解析大众点评项目失败: {e}")
                        continue
                        
            logger.info(f"从大众点评获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"大众点评爬取失败: {e}")
            
        return pois

    def crawl_58_pois(self, region_name: str, keyword: str, limit: int = 30) -> List[Dict]:
        """
        从58同城爬取兴趣点
        """
        pois = []
        try:
            # 58同城搜索URL
            search_url = f"https://search.58.com/{quote(region_name)}/{quote(keyword)}/"
            
            response = self.session.get(search_url, timeout=15)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 解析搜索结果
                result_items = soup.find_all('div', class_='des')
                
                for item in result_items[:limit]:
                    try:
                        name_elem = item.find('h3') or item.find('a')
                        name = name_elem.get_text().strip() if name_elem else ''
                        
                        # 提取地址信息
                        address = ''
                        addr_elem = item.find('p', class_='add')
                        if addr_elem:
                            address = addr_elem.get_text().strip()
                        
                        if name:
                            poi = {
                                'name': name,
                                'address': address,
                                'phone': '',
                                'website': '',
                                'amenity': keyword,
                                'shop': '',
                                'lat': '',
                                'lon': '',
                                'source': '58同城'
                            }
                            pois.append(poi)
                            
                    except Exception as e:
                        logger.warning(f"解析58同城项目失败: {e}")
                        continue
                        
            logger.info(f"从58同城获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"58同城爬取失败: {e}")
            
        return pois

    def crawl_pois(self, region_code: str, keyword: str, output_file: str = None) -> List[Dict]:
        """
        综合爬取兴趣点
        """
        region_name = self.get_region_name(region_code)
        logger.info(f"开始爬取地区: {region_name} ({region_code}), 关键词: {keyword}")
        
        all_pois = []
        
        # 从多个数据源爬取
        logger.info("正在从OpenStreetMap爬取...")
        osm_pois = self.crawl_osm_pois(region_name, keyword)
        all_pois.extend(osm_pois)
        
        time.sleep(random.uniform(1, 3))  # 随机延时
        
        logger.info("正在从大众点评爬取...")
        dianping_pois = self.crawl_dianping_pois(region_name, keyword)
        all_pois.extend(dianping_pois)
        
        time.sleep(random.uniform(1, 3))  # 随机延时
        
        logger.info("正在从58同城爬取...")
        pois_58 = self.crawl_58_pois(region_name, keyword)
        all_pois.extend(pois_58)
        
        # 去重
        unique_pois = self.deduplicate_pois(all_pois)
        
        logger.info(f"总共获取到 {len(unique_pois)} 个去重后的POI")
        
        # 保存结果
        if output_file:
            self.save_to_csv(unique_pois, output_file)
            
        return unique_pois

    def deduplicate_pois(self, pois: List[Dict]) -> List[Dict]:
        """去重POI数据"""
        seen_names = set()
        unique_pois = []
        
        for poi in pois:
            name = poi.get('name', '').strip()
            if name and name not in seen_names:
                seen_names.add(name)
                unique_pois.append(poi)
                
        return unique_pois

    def save_to_csv(self, pois: List[Dict], filename: str):
        """保存POI数据到CSV文件"""
        if not pois:
            logger.warning("没有POI数据可保存")
            return
            
        fieldnames = ['name', 'address', 'phone', 'website', 'amenity', 'shop', 'lat', 'lon', 'source']
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(pois)
            
        logger.info(f"POI数据已保存到: {filename}")

def main():
    """主函数"""
    crawler = POICrawler()
    
    # 示例用法
    region_code = "110000"  # 北京市
    keyword = "餐厅"
    output_file = f"pois_{region_code}_{keyword}.csv"
    
    # 爬取POI
    pois = crawler.crawl_pois(region_code, keyword, output_file)
    
    # 打印前几个结果
    print(f"\n获取到的前5个POI:")
    for i, poi in enumerate(pois[:5], 1):
        print(f"{i}. {poi['name']} - {poi['address']} ({poi['source']})")

if __name__ == "__main__":
    main()
