#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国POI爬虫 - 专门针对中国可用的数据源
"""

import requests
import json
import time
import csv
import re
import argparse
from urllib.parse import quote, urlencode
from bs4 import BeautifulSoup
import logging
from typing import List, Dict
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ChinaPOICrawler:
    def __init__(self):
        self.session = requests.Session()
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]
        self.update_headers()
        
        # 地区代码映射
        self.region_codes = {
            '110000': '北京',
            '120000': '天津',
            '310000': '上海',
            '500000': '重庆',
            '440100': '广州',
            '440300': '深圳',
            '330100': '杭州',
            '320100': '南京',
            '510100': '成都',
            '420100': '武汉',
            '610100': '西安',
            '430100': '长沙',
            '370200': '青岛',
            '350200': '厦门',
            '530100': '昆明',
            '450100': '南宁'
        }

    def update_headers(self):
        """更新请求头"""
        self.session.headers.update({
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        })

    def get_region_name(self, region_code: str) -> str:
        """根据地区代码获取地区名称"""
        return self.region_codes.get(region_code, region_code)

    def crawl_dianping_pois(self, region_name: str, keyword: str, limit: int = 30) -> List[Dict]:
        """
        从大众点评爬取POI数据
        """
        pois = []
        try:
            # 大众点评搜索URL - 使用移动版，更容易解析
            search_url = f"https://m.dianping.com/search/keyword/{quote(region_name)}/0_{quote(keyword)}"
            
            self.update_headers()
            self.session.headers.update({
                'Referer': 'https://m.dianping.com/',
                'Host': 'm.dianping.com'
            })
            
            response = self.session.get(search_url, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找商家列表
                shop_items = soup.find_all(['div', 'li'], class_=re.compile(r'shop|item|list'))
                
                for item in shop_items[:limit]:
                    try:
                        # 提取店名
                        name_elem = item.find(['h3', 'h4', 'a'], class_=re.compile(r'name|title'))
                        if not name_elem:
                            name_elem = item.find('a', title=True)
                        
                        name = ''
                        if name_elem:
                            name = name_elem.get_text().strip() or name_elem.get('title', '').strip()
                        
                        # 提取地址
                        addr_elem = item.find(['span', 'div', 'p'], class_=re.compile(r'addr|address|region'))
                        address = addr_elem.get_text().strip() if addr_elem else ''
                        
                        # 提取电话
                        phone_elem = item.find(['span', 'div'], class_=re.compile(r'tel|phone'))
                        phone = phone_elem.get_text().strip() if phone_elem else ''
                        
                        if name and len(name) > 1:
                            poi = {
                                'name': name,
                                'address': address,
                                'phone': phone,
                                'website': '',
                                'category': keyword,
                                'type': keyword,
                                'lat': '',
                                'lon': '',
                                'source': '大众点评'
                            }
                            pois.append(poi)
                            
                    except Exception as e:
                        continue
                        
            logger.info(f"从大众点评获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"大众点评爬取失败: {e}")
            
        return pois

    def crawl_58_pois(self, region_name: str, keyword: str, limit: int = 30) -> List[Dict]:
        """
        从58同城爬取POI数据
        """
        pois = []
        try:
            # 58同城搜索URL
            city_map = {
                '北京': 'bj',
                '上海': 'sh', 
                '广州': 'gz',
                '深圳': 'sz',
                '杭州': 'hz',
                '南京': 'nj',
                '成都': 'cd',
                '武汉': 'wh',
                '西安': 'xa',
                '长沙': 'cs'
            }
            
            city_code = city_map.get(region_name, 'bj')
            search_url = f"https://{city_code}.58.com/sou/?key={quote(keyword)}"
            
            self.update_headers()
            self.session.headers.update({
                'Referer': f'https://{city_code}.58.com/',
                'Host': f'{city_code}.58.com'
            })
            
            response = self.session.get(search_url, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找结果列表
                result_items = soup.find_all(['div', 'li'], class_=re.compile(r'des|item|list'))
                
                for item in result_items[:limit]:
                    try:
                        # 提取标题
                        title_elem = item.find(['h3', 'h4', 'a'], class_=re.compile(r'title|name'))
                        if not title_elem:
                            title_elem = item.find('a', title=True)
                        
                        name = ''
                        if title_elem:
                            name = title_elem.get_text().strip() or title_elem.get('title', '').strip()
                        
                        # 提取地址
                        addr_elem = item.find(['span', 'div', 'p'], class_=re.compile(r'add|address|area'))
                        address = addr_elem.get_text().strip() if addr_elem else ''
                        
                        if name and len(name) > 1:
                            poi = {
                                'name': name,
                                'address': address,
                                'phone': '',
                                'website': '',
                                'category': keyword,
                                'type': keyword,
                                'lat': '',
                                'lon': '',
                                'source': '58同城'
                            }
                            pois.append(poi)
                            
                    except Exception as e:
                        continue
                        
            logger.info(f"从58同城获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"58同城爬取失败: {e}")
            
        return pois

    def crawl_meituan_pois(self, region_name: str, keyword: str, limit: int = 30) -> List[Dict]:
        """
        从美团爬取POI数据
        """
        pois = []
        try:
            # 美团搜索URL
            search_url = f"https://www.meituan.com/s/{quote(keyword)}"
            
            self.update_headers()
            self.session.headers.update({
                'Referer': 'https://www.meituan.com/',
                'Host': 'www.meituan.com'
            })
            
            params = {
                'region': region_name
            }
            
            response = self.session.get(search_url, params=params, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找商家信息
                shop_items = soup.find_all(['div', 'li'], class_=re.compile(r'shop|poi|item'))
                
                for item in shop_items[:limit]:
                    try:
                        # 提取店名
                        name_elem = item.find(['h3', 'h4', 'a'], class_=re.compile(r'name|title'))
                        if not name_elem:
                            name_elem = item.find('a', title=True)
                        
                        name = ''
                        if name_elem:
                            name = name_elem.get_text().strip() or name_elem.get('title', '').strip()
                        
                        # 提取地址
                        addr_elem = item.find(['span', 'div'], class_=re.compile(r'addr|address|location'))
                        address = addr_elem.get_text().strip() if addr_elem else ''
                        
                        if name and len(name) > 1:
                            poi = {
                                'name': name,
                                'address': address,
                                'phone': '',
                                'website': '',
                                'category': keyword,
                                'type': keyword,
                                'lat': '',
                                'lon': '',
                                'source': '美团'
                            }
                            pois.append(poi)
                            
                    except Exception as e:
                        continue
                        
            logger.info(f"从美团获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"美团爬取失败: {e}")
            
        return pois

    def crawl_ganji_pois(self, region_name: str, keyword: str, limit: int = 20) -> List[Dict]:
        """
        从赶集网爬取POI数据
        """
        pois = []
        try:
            # 赶集网搜索URL
            city_map = {
                '北京': 'bj',
                '上海': 'sh', 
                '广州': 'gz',
                '深圳': 'sz',
                '杭州': 'hz',
                '南京': 'nj',
                '成都': 'cd',
                '武汉': 'wh',
                '西安': 'xa',
                '长沙': 'cs'
            }
            
            city_code = city_map.get(region_name, 'bj')
            search_url = f"https://{city_code}.ganji.com/sou/k{quote(keyword)}/"
            
            self.update_headers()
            response = self.session.get(search_url, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找结果列表
                result_items = soup.find_all(['div', 'li'], class_=re.compile(r'f-list|item'))
                
                for item in result_items[:limit]:
                    try:
                        # 提取标题
                        title_elem = item.find(['h3', 'a'], class_=re.compile(r'title|name'))
                        name = title_elem.get_text().strip() if title_elem else ''
                        
                        # 提取地址
                        addr_elem = item.find(['span', 'div'], class_=re.compile(r'address|area'))
                        address = addr_elem.get_text().strip() if addr_elem else ''
                        
                        if name and len(name) > 1:
                            poi = {
                                'name': name,
                                'address': address,
                                'phone': '',
                                'website': '',
                                'category': keyword,
                                'type': keyword,
                                'lat': '',
                                'lon': '',
                                'source': '赶集网'
                            }
                            pois.append(poi)
                            
                    except Exception as e:
                        continue
                        
            logger.info(f"从赶集网获取到 {len(pois)} 个POI")
            
        except Exception as e:
            logger.error(f"赶集网爬取失败: {e}")
            
        return pois

    def crawl_pois(self, region_code: str, keyword: str, output_file: str = None) -> List[Dict]:
        """
        综合爬取POI数据
        """
        region_name = self.get_region_name(region_code)
        logger.info(f"开始爬取地区: {region_name} ({region_code}), 关键词: {keyword}")
        
        all_pois = []
        
        # 1. 从大众点评获取数据
        logger.info("正在从大众点评获取数据...")
        try:
            dianping_pois = self.crawl_dianping_pois(region_name, keyword, 20)
            all_pois.extend(dianping_pois)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"大众点评数据获取失败: {e}")
        
        # 2. 从58同城获取数据
        logger.info("正在从58同城获取数据...")
        try:
            pois_58 = self.crawl_58_pois(region_name, keyword, 15)
            all_pois.extend(pois_58)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"58同城数据获取失败: {e}")
        
        # 3. 从美团获取数据
        logger.info("正在从美团获取数据...")
        try:
            meituan_pois = self.crawl_meituan_pois(region_name, keyword, 15)
            all_pois.extend(meituan_pois)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"美团数据获取失败: {e}")
        
        # 4. 从赶集网获取数据
        logger.info("正在从赶集网获取数据...")
        try:
            ganji_pois = self.crawl_ganji_pois(region_name, keyword, 10)
            all_pois.extend(ganji_pois)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"赶集网数据获取失败: {e}")
        
        # 去重和清理
        unique_pois = self.deduplicate_pois(all_pois)
        cleaned_pois = self.clean_pois(unique_pois)
        
        logger.info(f"总共获取到 {len(cleaned_pois)} 个去重清理后的POI")
        
        # 保存结果
        if output_file:
            self.save_to_csv(cleaned_pois, output_file)
            
        return cleaned_pois

    def deduplicate_pois(self, pois: List[Dict]) -> List[Dict]:
        """去重POI数据"""
        seen = set()
        unique_pois = []
        
        for poi in pois:
            # 使用名称作为去重键
            name = poi.get('name', '').strip()
            if name and name not in seen:
                seen.add(name)
                unique_pois.append(poi)
                
        return unique_pois

    def clean_pois(self, pois: List[Dict]) -> List[Dict]:
        """清理POI数据"""
        cleaned_pois = []
        
        for poi in pois:
            # 清理名称
            name = poi.get('name', '').strip()
            if not name or len(name) < 2:
                continue
                
            # 过滤掉明显的垃圾数据
            if any(x in name.lower() for x in ['广告', '推广', '置顶', '热门', '招聘', '转让']):
                continue
                
            # 清理电话号码
            phone = poi.get('phone', '').strip()
            phone = re.sub(r'[^\d\-\+\(\)\s]', '', phone)
            
            # 清理地址
            address = poi.get('address', '').strip()
            
            poi.update({
                'name': name,
                'phone': phone,
                'address': address
            })
            
            cleaned_pois.append(poi)
            
        return cleaned_pois

    def save_to_csv(self, pois: List[Dict], filename: str):
        """保存POI数据到CSV文件"""
        if not pois:
            logger.warning("没有POI数据可保存")
            return
            
        fieldnames = ['name', 'address', 'phone', 'website', 'category', 'type', 'lat', 'lon', 'source']
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(pois)
            
        logger.info(f"POI数据已保存到: {filename}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='中国POI数据爬虫')
    parser.add_argument('--region', '-r', help='地区代码')
    parser.add_argument('--keyword', '-k', help='搜索关键词')
    parser.add_argument('--output', '-o', help='输出文件路径')
    
    args = parser.parse_args()
    
    crawler = ChinaPOICrawler()
    
    # 如果没有提供参数，使用默认值
    region_code = args.region or "110000"  # 默认北京
    keyword = args.keyword or "餐厅"       # 默认餐厅
    output_file = args.output or f"china_pois_{region_code}_{keyword}.csv"
    
    print(f"开始爬取 {crawler.get_region_name(region_code)} 的 {keyword} 数据...")
    print("支持的地区: 北京、上海、广州、深圳、杭州、南京、成都、武汉、西安、长沙等")
    print("支持的关键词: 餐厅、酒店、超市、医院、学校、银行等")
    print("-" * 60)
    
    # 爬取POI
    pois = crawler.crawl_pois(region_code, keyword, output_file)
    
    # 显示结果
    if pois:
        print(f"\n成功获取到 {len(pois)} 个POI:")
        print("-" * 50)
        
        # 按数据源统计
        sources = {}
        for poi in pois:
            source = poi.get('source', '未知')
            sources[source] = sources.get(source, 0) + 1
        
        print("数据源统计:")
        for source, count in sources.items():
            print(f"  {source}: {count} 个")
        
        print(f"\n前10个POI:")
        for i, poi in enumerate(pois[:10], 1):
            print(f"{i:2d}. {poi['name']}")
            if poi['address']:
                print(f"     地址: {poi['address']}")
            if poi['phone']:
                print(f"     电话: {poi['phone']}")
            print(f"     来源: {poi['source']}")
            print()
            
        print(f"完整数据已保存到: {output_file}")
    else:
        print("未获取到任何POI数据，可能的原因:")
        print("1. 网络连接问题")
        print("2. 网站结构发生变化")
        print("3. 被反爬虫机制拦截")
        print("4. 关键词或地区不支持")

if __name__ == "__main__":
    main()
